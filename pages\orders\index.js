import orderApi from '../../api/modules/order';
import { OrderStatus } from '../../common/constant';
import { formatNormalDate } from '../utils/util';

Page({
  data: {
    userInfo: null,
    orderStatus: OrderStatus,
    // 订单类型标签（今日订单/总订单）
    orderTypeTabs: [
      { name: '今日订单', type: 'today' },
      { name: '总订单', type: 'all' },
    ],
    currentOrderType: 'today', // 当前选中的订单类型
    // 订单状态标签
    orderTabs: [
      {
        name: OrderStatus.待服务,
        status: OrderStatus.待服务,
      },
      {
        name: OrderStatus.服务中,
        status: `${OrderStatus.已出发},${OrderStatus.服务中}`,
      },
      {
        name: OrderStatus.已完成,
        status: OrderStatus.已完成,
      },
      {
        name: OrderStatus.已取消,
        status: OrderStatus.已取消,
      },
    ],
    currentTab: OrderStatus.待服务, // 当前选中的标签
    orderList: [], // 订单列表
    allOrdersCache: [], // 缓存所有订单数据用于分页
    page: 1, // 当前页码
    pageSize: 10, // 每页数量
    hasMore: true, // 是否还有更多数据
    loading: false, // 是否正在加载
    // 时间选择器相关
    showTimePicker: false, // 是否显示时间选择器
    selectedTime: '', // 选择的时间
    currentOrderId: '', // 当前操作的订单ID
  },

  onLoad() {
    // 从本地存储获取用户信息
    const userInfo = wx.getStorageSync('userInfo');
    console.log(userInfo);

    this.setData({
      userInfo: userInfo
    });

    if (!userInfo) {
      return wx.redirectTo({
        url: '/pages/login/index',
      });
    } else {
      // 加载初始订单数据
      this.loadOrders();
    }

    // 使用全局socket管理器
    const app = getApp();
    const socketManager = app.globalData.socketManager;

    // 注册消息处理器
    socketManager.registerMessageHandler('orders', (notification) => {
      this.handleSocketMessage(notification);
    });

    // 确保socket连接
    if (!socketManager.isConnected()) {
      socketManager.connect();
    }
  },

  // 切换订单类型（今日订单/总订单）
  switchOrderType(e) {
    const type = e.currentTarget.dataset.type;
    this.setData({
      currentOrderType: type,
      page: 1,
      orderList: [],
      allOrdersCache: [],
      hasMore: true,
      loading: false,
    });
    this.loadOrders();
  },

  // 切换订单状态标签
  switchTab(e) {
    const status = e.currentTarget.dataset.status;
    this.setData({
      currentTab: status,
      page: 1, // 重置页码
      orderList: [], // 清空当前列表
      allOrdersCache: [], // 清空缓存
      hasMore: true,
      loading: false,
    });
    this.loadOrders();
  },

  // 判断是否为今天或之前的日期
  isTodayOrBefore(dateStr) {
    if (!dateStr) return false;
    const targetDate = new Date(dateStr);
    const today = new Date();
    today.setHours(23, 59, 59, 999); // 设置为今天的最后一刻
    return targetDate <= today;
  },

  // 筛选今日订单
  filterTodayOrders(orders) {
    return orders.filter(order => this.isTodayOrBefore(order.serviceTime));
  },

  // 重置分页状态并重新加载
  resetAndReload() {
    this.setData({
      page: 1,
      orderList: [],
      allOrdersCache: [],
      hasMore: true,
      loading: false,
    });
    this.loadOrders();
  },

  // 加载订单数据
  loadOrders() {
    if (this.data.loading) return;

    this.setData({ loading: true });

    orderApi
      .myList(this.data.userInfo.id, this.data.currentTab)
      .then(res => {
        let allOrders = (res?.list || []).map(item => {
          return {
            // 保留原始数据
            ...item,
            // 添加格式化后的显示数据
            orderId: item.id,
            orderNumber: item.sn,
            status: item.status,
            statusText: item.status,
            productName: item.orderDetails?.[0].service.serviceName,
            productImage: item.orderDetails?.[0].service.logo,
            petName: item.orderDetails?.[0].petName,
            userAdress: item.addressDetail + '(' + item.addressRemark + ')',
            quantity: 1,
            expectTime: formatNormalDate(item.serviceTime),
            serviceTime: item.serviceTime, // 保留原始时间用于筛选
            extraServive: (item.orderDetails?.[0].additionalServices || []).map(v => v.name),
          };
        });

        // 如果是今日订单，进行日期筛选
        if (this.data.currentOrderType === 'today') {
          allOrders = this.filterTodayOrders(allOrders);
          // 今日订单显示全部，不分页
          this.setData({
            orderList: allOrders,
            hasMore: false,
            loading: false,
            allOrdersCache: allOrders, // 缓存所有订单用于分页
          });
        } else {
          // 总订单需要分页
          const { page, pageSize } = this.data;
          const startIndex = (page - 1) * pageSize;
          const endIndex = startIndex + pageSize;
          const pageData = allOrders.slice(startIndex, endIndex);

          // 判断是否还有更多数据
          const hasMore = endIndex < allOrders.length;

          this.setData({
            orderList: page === 1 ? pageData : [...this.data.orderList, ...pageData],
            hasMore,
            loading: false,
            allOrdersCache: allOrders, // 缓存所有订单用于分页
          });
        }
      })
      .catch(err => {
        console.error('加载订单失败:', err);
        this.setData({ loading: false });
      });
  },

  // 加载更多订单
  loadMoreOrders() {
    // 只有总订单才支持分页加载更多
    if (this.data.currentOrderType === 'all' && this.data.hasMore && !this.data.loading) {
      const { page, pageSize, allOrdersCache } = this.data;

      // 确保缓存数据存在
      if (!allOrdersCache || allOrdersCache.length === 0) {
        return;
      }

      const nextPage = page + 1;
      const startIndex = (nextPage - 1) * pageSize;
      const endIndex = startIndex + pageSize;
      const pageData = allOrdersCache.slice(startIndex, endIndex);

      // 判断是否还有更多数据
      const hasMore = endIndex < allOrdersCache.length;

      this.setData({
        page: nextPage,
        orderList: [...this.data.orderList, ...pageData],
        hasMore,
      });
    }
  },

  // 切换更多操作弹窗
  toggleOrderActions(e) {
    const orderId = e.currentTarget.dataset.id;
    const orderList = this.data.orderList.map(order => {
      if (order.orderId === orderId) {
        order.showMoreActions = !order.showMoreActions;
      } else {
        // 关闭其他订单的更多操作
        order.showMoreActions = false;
      }
      return order;
    });

    this.setData({
      orderList,
    });
  },

  // 阻止事件冒泡
  preventTap(e) {
    e.stopPropagation();
  },

  // 查看订单详情
  viewOrderDetail(e) {
    const orderId = e.currentTarget.dataset.id;
    console.log('查看订单详情', orderId);
    console.log('orderList: ', this.data.orderList);
    // 找到对应的订单数据
    const orderInfo = this.data.orderList.find(order => order.orderId === orderId);
    console.log('orderInfo: ', orderInfo);
    if (orderInfo) {
      // 将订单信息存储到本地存储，供详情页使用
      wx.setStorageSync('orderInfo', orderInfo);
    }
    wx.navigateTo({
      url: `/pages/orders/orderDetail/index?orderId=${orderId}`,
    });
  },

  // 修改上门时间
  reschedule(e) {
    const orderId = e.currentTarget.dataset.id;
    // 显示时间选择器
    this.setData({
      showTimePicker: true,
      currentOrderId: orderId,
      selectedTime: '',
    });
  },

  // 时间选择器确认
  onTimeSelected(e) {
    const selectedTime = e.detail;
    this.setData({
      selectedTime,
    });

    // 确认修改时间
    if (selectedTime) {
      this.confirmUpdateTime();
    }
  },

  // 确认修改时间
  async confirmUpdateTime() {
    if (!this.data.selectedTime) {
      wx.showToast({
        title: '请选择有效时间',
        icon: 'none',
      });
      return;
    }

    wx.showLoading({
      title: '修改中...',
    });

    try {
      const res = await orderApi.updateServiceTime(
        this.data.currentOrderId,
        this.data.userInfo.id,
        this.data.selectedTime
      );

      if (res) {
        wx.showToast({
          title: '修改成功',
          icon: 'success',
        });
        // 重新加载订单列表
        this.resetAndReload();
      } else {
        wx.showToast({
          title: '修改失败',
          icon: 'error',
        });
      }
    } catch (error) {
      wx.showToast({
        title: '修改失败',
        icon: 'error',
      });
    } finally {
      wx.hideLoading();
      // 关闭时间选择器
      this.setData({
        showTimePicker: false,
        currentOrderId: '',
        selectedTime: '',
      });
    }
  },

  // 取消时间选择
  onTimeCancel() {
    this.setData({
      showTimePicker: false,
      currentOrderId: '',
      selectedTime: '',
    });
  },

  // 出发
  dispatch(e) {
    const orderId = e.currentTarget.dataset.id;

    // 弹窗确认
    wx.showModal({
      title: '确认出发',
      content: '确定要开始出发吗？',
      success: async res => {
        if (res.confirm) {
          wx.showLoading({
            title: '处理中...',
          });

          try {
            const result = await orderApi.dispatch(orderId, this.data.userInfo.id);
            if (result) {
              wx.showToast({
                title: '已开始出发',
                icon: 'success',
              });
              // 刷新订单列表
              this.resetAndReload();
            } else {
              wx.showToast({
                title: '操作失败',
                icon: 'error',
              });
            }
          } catch (error) {
            wx.showToast({
              title: '操作失败',
              icon: 'error',
            });
            console.error('出发操作失败:', error);
          } finally {
            wx.hideLoading();
          }
        }
      },
    });
  },

  // 开始服务
  start(e) {
    const orderId = e.currentTarget.dataset.id;

    // 弹窗确认
    wx.showModal({
      title: '确认开始服务',
      content: '确定要开始提供服务吗？',
      success: async res => {
        if (res.confirm) {
          wx.showLoading({
            title: '处理中...',
          });

          try {
            const result = await orderApi.start(orderId, this.data.userInfo.id);
            if (result) {
              wx.showToast({
                title: '已开始服务',
                icon: 'success',
              });
              // 刷新订单列表
              this.resetAndReload();
            } else {
              wx.showToast({
                title: '操作失败',
                icon: 'error',
              });
            }
          } catch (error) {
            wx.showToast({
              title: '操作失败',
              icon: 'error',
            });
            console.error('开始服务操作失败:', error);
          } finally {
            wx.hideLoading();
          }
        }
      },
    });
  },

  // 结束服务
  complete(e) {
    const orderId = e.currentTarget.dataset.id;

    // 弹窗确认
    wx.showModal({
      title: '确认完成服务',
      content: '确定已完成所有服务内容吗？',
      success: async res => {
        if (res.confirm) {
          wx.showLoading({
            title: '处理中...',
          });

          try {
            const result = await orderApi.complete(orderId, this.data.userInfo.id);
            if (result) {
              wx.showToast({
                title: '服务已完成',
                icon: 'success',
              });
              // 刷新订单列表
              this.resetAndReload();
            } else {
              wx.showToast({
                title: '操作失败',
                icon: 'error',
              });
            }
          } catch (error) {
            wx.showToast({
              title: '操作失败',
              icon: 'error',
            });
            console.error('完成服务操作失败:', error);
          } finally {
            wx.hideLoading();
          }
        }
      },
    });
  },

  // 处理Socket消息
  handleSocketMessage(notification) {
    console.log('订单页面收到Socket消息:', notification);

    switch (notification.type) {
      case 'new_order':
        // 更新订单列表
        this.resetAndReload();
        break;
      case 'cancel_order':
        // 更新订单列表
        this.resetAndReload();
        break;
      case 'order_status_change':
        // 订单状态变更，更新列表
        this.resetAndReload();
        break;
      case 'message':
        // 消息已在socketManager中处理
        break;
    }
  },

  // 页面卸载时注销消息处理器
  onUnload() {
    const app = getApp();
    const socketManager = app.globalData.socketManager;
    socketManager.unregisterMessageHandler('orders');
  },
});
