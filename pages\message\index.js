// pages/message/index.js
const utils = require('../utils/util');
Page({

  /**
   * 页面的初始数据
   */
  data: {
    userInfo: null,
    newsList: [],
    // newsList: [{
    //   type: 'system',
    //   title: '系统通知',
    //   content: '您有指定订单待接单。',
    //   time: utils.formatDate(new Date('2025-04-03T12:30:00').getTime()),
    //   count: 2
    // }]
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad(options) {
    const userInfo = this.data.userInfo;
    console.log(userInfo);
    if (!userInfo) {
      return wx.redirectTo({
        url: "/pages/login/index",
      });
    }
  },

  /**
   * 生命周期函数--监听页面初次渲染完成
   */
  onReady() {

  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow() {

  },

  /**
   * 生命周期函数--监听页面隐藏
   */
  onHide() {

  },

  /**
   * 生命周期函数--监听页面卸载
   */
  onUnload() {

  },

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh() {

  },

  /**
   * 页面上拉触底事件的处理函数
   */
  onReachBottom() {

  },

  /**
   * 用户点击右上角分享
   */
  onShareAppMessage() {

  }
})