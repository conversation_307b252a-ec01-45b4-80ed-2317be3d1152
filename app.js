require('./page-extend');
import socketManager from './utils/socketManager';

App({
	globalData: {
		userInfo: null,
		tabBar: [],
		homePage: '/pages/index',
		pages: ['/pages/index', '/pages/index/service/second'],
		userData: {},
		socketManager: socketManager // 全局socket管理器
	},
	onLaunch() {
		wx.getSystemInfo({
			success: (e) => {
				this.globalData.StatusBar = e.statusBarHeight;
				let capsule = wx.getMenuButtonBoundingClientRect();
				this.globalData.WindowWidth = e.windowWidth;
				this.globalData.PixelRatio = 750 / e.windowWidth;
				if (capsule) {
					this.globalData.Custom = capsule;
					this.globalData.CustomBar = capsule.bottom + capsule.top - e.statusBarHeight;
				} else {
					this.globalData.CustomBar = e.statusBarHeight + 50;
				}
			}
		});

		// 检查用户登录状态，如果已登录则初始化socket连接
		const userInfo = wx.getStorageSync('userInfo');
		if (userInfo) {
			console.log('用户已登录，初始化Socket连接');
			socketManager.connect();
		}
	},
	onShow() {
		// 应用从后台进入前台时，检查socket连接
		const userInfo = wx.getStorageSync('userInfo');
		if (userInfo && !socketManager.isConnected()) {
			console.log('应用进入前台，重新连接Socket');
			socketManager.connect();
		}
	},
	onHide() {
		// 应用进入后台时保持socket连接，不断开
		console.log('应用进入后台，保持Socket连接');
	}
});
