import { formatNormalDate } from '../../utils/util';
Page({
  data: {
    orderDetail: {}, // 订单
    showMoreActions: false,
  },

  onLoad() {
    const info = wx.getStorageSync('orderInfo');
    if (info) {
      this.setData({
        orderDetail: {
          ...info,
          additionalServices: info.orderDetails.flatMap(detail => detail.additionalServices?.map(v => v.name) || []),
          petName: info.orderDetails.map(item => item.petName)[0],
          serviceTime: info.serviceTime ? formatNormalDate(info.serviceTime) : null,
          // 下单时间
          orderTime: info.orderTime ? formatNormalDate(info.orderTime) : null,
          createdAt: info.createdAt ? formatNormalDate(info.createdAt) : null,
        },
      });
    }
  },
  // 加载订单数据
  loadOrders() {
    wx.showLoading({
      title: '加载中',
    });

    this.setData({
      orderDetail: mockOrder,
    });

    wx.hideLoading();
  },
  // 获取订单状态文字
  getOrderStatusText(status) {
    const statusMap = {
      unpaid: '待付款',
      paid: '待接单',
      completed: '进行中',
      review: '待评价',
    };
    return statusMap[status] || '未知状态';
  },
  // 切换更多操作弹窗
  toggleOrderActions(e) {
    this.setData({
      showMoreActions: !this.data.showMoreActions,
    });
  },
  // 提交订单
  submitOrder() {
    // 验证表单信息
    if (!this.validateForm()) {
      wx.showToast({
        title: '请填写完整信息',
        icon: 'none',
      });
      return;
    }

    // 模拟订单提交
    wx.showLoading({
      title: '提交订单中',
    });

    // 这里应该调用后端API提交订单
    setTimeout(() => {
      wx.hideLoading();
      wx.showToast({
        title: '订单提交成功',
        icon: 'success',
        duration: 2000,
        success: () => {
          // 跳转到订单列表或详情页
          wx.navigateTo({
            url: '/pages/orderList/orderList',
          });
        },
      });
    }, 1500);
  },

  // 取消订单
  cancelOrder() {
    wx.navigateBack({
      delta: 1,
    });
  },

  // 表单验证
  validateForm() {
    const { customerName, customerPhone, customerAddress, quantity } = this.data;
    return customerName && customerPhone && customerAddress && quantity > 0;
  },
});
