import { formatNormalDate } from '../../utils/util';
import orderApi from '../../../api/modules/order';

Page({
  data: {
    orderDetail: {}, // 订单
    showMoreActions: false,
    // 时间选择器相关
    showTimePicker: false, // 是否显示时间选择器
    selectedTime: '', // 选择的时间
    userInfo: null, // 用户信息
  },

  onLoad(options) {
    // 获取用户信息
    const userInfo = wx.getStorageSync('userInfo');
    this.setData({ userInfo });

    // 如果有传递orderId参数，优先使用API获取订单详情
    if (options.orderId) {
      this.loadOrderDetail(options.orderId);
    } else {
      // 否则从本地存储获取
      const info = wx.getStorageSync('orderInfo');
      if (info) {
        this.setOrderDetail(info);
      }
    }
  },

  // 设置订单详情数据
  setOrderDetail(info) {
    // 计算订单总金额
    const totalAmount = info.orderDetails?.reduce((total, detail) => {
      const servicePrice = detail.service?.price || 0;
      const additionalPrice = detail.additionalServices?.reduce((sum, service) => sum + (service.price || 0), 0) || 0;
      return total + servicePrice + additionalPrice;
    }, 0) || 0;

    this.setData({
      orderDetail: {
        ...info,
        additionalServices:
          info.orderDetails?.flatMap(detail => detail.additionalServices?.map(v => v.name) || []) || [],
        petName: info.orderDetails?.map(item => item.petName)[0] || '',
        serviceTime: info.serviceTime ? formatNormalDate(info.serviceTime) : null,
        // 下单时间
        orderTime: info.orderTime ? formatNormalDate(info.orderTime) : null,
        createdAt: info.createdAt ? formatNormalDate(info.createdAt) : null,
        totalAmount: totalAmount, // 订单总金额
        formattedAmount: `¥${totalAmount.toFixed(2)}`, // 格式化后的金额
      },
    });
  },

  // 通过API加载订单详情
  async loadOrderDetail(orderId) {
    console.log('orderId: ', orderId);
    wx.showLoading({ title: '加载中...' });

    try {
      // 首先尝试从本地存储获取（订单列表页面已经存储了数据）
      const info = wx.getStorageSync('orderInfo');
      if (info && (info.id == orderId || info.orderId == orderId)) {
        this.setOrderDetail(info);
        wx.hideLoading();
        return;
      }

      // 如果本地存储没有，则调用API获取订单详情
      const orderDetail = await orderApi.myList(this.data.userInfo.id);
      if (orderDetail && orderDetail.list) {
        // 从订单列表中找到对应的订单
        const targetOrder = orderDetail.list.find(order => order.id == orderId);
        if (targetOrder) {
          // 格式化订单数据（与订单列表页面保持一致）
          const formattedOrder = {
            ...targetOrder,
            orderId: targetOrder.id,
            orderNumber: targetOrder.sn,
            status: targetOrder.status,
            statusText: targetOrder.status,
            productName: targetOrder.orderDetails?.[0].service.serviceName,
            productImage: targetOrder.orderDetails?.[0].service.logo,
            petName: targetOrder.orderDetails?.[0].petName,
            userAdress: targetOrder.addressDetail + '(' + targetOrder.addressRemark + ')',
            quantity: 1,
            expectTime: formatNormalDate(targetOrder.serviceTime),
            serviceTime: targetOrder.serviceTime,
            extraServive: (targetOrder.orderDetails?.[0].additionalServices || []).map(v => v.name),
          };

          this.setOrderDetail(formattedOrder);
          // 更新本地存储
          wx.setStorageSync('orderInfo', formattedOrder);
        } else {
          wx.showToast({
            title: '订单不存在',
            icon: 'none',
          });
        }
      } else {
        wx.showToast({
          title: '获取订单信息失败',
          icon: 'none',
        });
      }
    } catch (error) {
      console.error('加载订单详情失败:', error);
      wx.showToast({
        title: '加载失败',
        icon: 'none',
      });
    } finally {
      wx.hideLoading();
    }
  },
  // 获取订单状态文字
  getOrderStatusText(status) {
    const statusMap = {
      unpaid: '待付款',
      paid: '待接单',
      completed: '进行中',
      review: '待评价',
    };
    return statusMap[status] || '未知状态';
  },
  // 切换更多操作弹窗
  toggleOrderActions(e) {
    this.setData({
      showMoreActions: !this.data.showMoreActions,
    });
  },
  // 提交订单
  submitOrder() {
    // 验证表单信息
    if (!this.validateForm()) {
      wx.showToast({
        title: '请填写完整信息',
        icon: 'none',
      });
      return;
    }

    // 模拟订单提交
    wx.showLoading({
      title: '提交订单中',
    });

    // 这里应该调用后端API提交订单
    setTimeout(() => {
      wx.hideLoading();
      wx.showToast({
        title: '订单提交成功',
        icon: 'success',
        duration: 2000,
        success: () => {
          // 跳转到订单列表或详情页
          wx.navigateTo({
            url: '/pages/orderList/orderList',
          });
        },
      });
    }, 1500);
  },

  // 取消订单
  cancelOrder() {
    wx.navigateBack({
      delta: 1,
    });
  },

  // 表单验证
  validateForm() {
    const { customerName, customerPhone, customerAddress, quantity } = this.data;
    return customerName && customerPhone && customerAddress && quantity > 0;
  },

  // 联系客户
  contactCustomer() {
    const { orderDetail } = this.data;

    // 检查订单状态
    if (orderDetail.status !== '待服务') {
      wx.showToast({
        title: '只有待服务状态的订单才能联系客户',
        icon: 'none',
      });
      return;
    }

    const phoneNumber = orderDetail.customer?.phone || orderDetail.customer?.mobile;

    if (!phoneNumber) {
      wx.showToast({
        title: '客户手机号不存在',
        icon: 'none',
      });
      return;
    }

    wx.showModal({
      title: '联系客户',
      content: `确定要拨打客户电话 ${phoneNumber} 吗？`,
      success: res => {
        if (res.confirm) {
          wx.makePhoneCall({
            phoneNumber: phoneNumber,
            success: () => {
              console.log('拨打电话成功');
            },
            fail: err => {
              console.error('拨打电话失败:', err);
              wx.showToast({
                title: '拨打电话失败',
                icon: 'none',
              });
            },
          });
        }
      },
    });
  },

  // 修改上门时间
  reschedule() {
    const { orderDetail } = this.data;

    // 检查订单状态
    if (orderDetail.status !== '待服务') {
      wx.showToast({
        title: '只有待服务状态的订单才能修改上门时间',
        icon: 'none',
      });
      return;
    }

    this.setData({
      showTimePicker: true,
      selectedTime: '',
    });
  },

  // 时间选择器确认
  onTimeSelected(e) {
    const selectedTime = e.detail;
    this.setData({
      selectedTime,
    });

    // 确认修改时间
    if (selectedTime) {
      this.confirmUpdateTime();
    }
  },

  // 确认修改时间
  async confirmUpdateTime() {
    if (!this.data.selectedTime) {
      wx.showToast({
        title: '请选择有效时间',
        icon: 'none',
      });
      return;
    }

    wx.showLoading({
      title: '修改中...',
    });

    try {
      const res = await orderApi.updateServiceTime(
        this.data.orderDetail.orderId || this.data.orderDetail.id,
        this.data.userInfo.id,
        new Date(this.data.selectedTime).toISOString(),
      );

      if (res) {
        wx.showToast({
          title: '修改成功',
          icon: 'success',
        });

        // 更新本地订单数据
        const updatedOrderDetail = {
          ...this.data.orderDetail,
          serviceTime: formatNormalDate(this.data.selectedTime),
        };
        this.setData({
          orderDetail: updatedOrderDetail,
        });

        // 更新本地存储
        wx.setStorageSync('orderInfo', updatedOrderDetail);
      } else {
        wx.showToast({
          title: '修改失败',
          icon: 'error',
        });
      }
    } catch (error) {
      wx.showToast({
        title: '修改失败',
        icon: 'error',
      });
    } finally {
      wx.hideLoading();
      // 关闭时间选择器
      this.setData({
        showTimePicker: false,
        selectedTime: '',
      });
    }
  },

  // 取消时间选择
  onTimeCancel() {
    this.setData({
      showTimePicker: false,
      selectedTime: '',
    });
  },
});
