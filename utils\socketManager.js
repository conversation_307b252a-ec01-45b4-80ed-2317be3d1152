import config from '../api/config';

class SocketManager {
  constructor() {
    this.socketOpen = false;
    this.reconnectTimer = null;
    this.messageHandlers = new Map(); // 存储不同页面的消息处理器
    this.reconnectAttempts = 0;
    this.maxReconnectAttempts = 5;
  }

  // 建立WebSocket连接
  connect() {
    // 检查是否已有连接
    if (this.socketOpen) {
      console.log('Socket已连接，无需重复连接');
      return;
    }

    const socketUrl = config.socketUrl;
    console.log('正在连接Socket:', socketUrl);

    // 打开WebSocket连接
    wx.connectSocket({
      url: socketUrl,
      success: (res) => {
        this.socketOpen = true;
        this.reconnectAttempts = 0;
        console.log('WebSocket连接成功');
      },
      fail: (err) => {
        console.error('WebSocket连接失败', err);
        this.reconnect();
      },
    });

    // 监听WebSocket消息
    wx.onSocketMessage((res) => {
      this.handleMessage(res.data);
    });

    // 监听WebSocket关闭
    wx.onSocketClose((res) => {
      this.socketOpen = false;
      console.log('WebSocket连接关闭', res);
      this.reconnect();
    });

    // 监听WebSocket错误
    wx.onSocketError((res) => {
      console.error('WebSocket连接错误', res);
      this.reconnect();
    });
  }

  // 处理接收到的消息
  handleMessage(message) {
    try {
      const notification = JSON.parse(message);
      console.log('收到Socket消息:', notification);

      // 播放通知音效
      this.playNotificationSound(notification.type);

      // 通知所有注册的消息处理器
      this.messageHandlers.forEach((handler, pageId) => {
        try {
          handler(notification);
        } catch (error) {
          console.error(`页面 ${pageId} 处理消息失败:`, error);
        }
      });

      // 显示系统消息
      if (notification.type === 'message') {
        wx.showToast({
          title: notification.message,
          icon: 'none',
          duration: 2000,
        });
      }
    } catch (error) {
      console.error('解析Socket消息失败:', error);
    }
  }

  // 播放通知音效
  playNotificationSound(type) {
    const audioContext = wx.createInnerAudioContext();
    if (type === 'new_order') {
      audioContext.src = 'https://xian7.zos.ctyun.cn/pet/static/message.mp3';
    } else if (type === 'cancel_order') {
      audioContext.src = 'https://xian7.zos.ctyun.cn/pet/static/message1.mp3';
    }
    
    if (audioContext.src) {
      audioContext.autoplay = true;
      audioContext.play();
    }
  }

  // 重连WebSocket
  reconnect() {
    if (this.reconnectAttempts >= this.maxReconnectAttempts) {
      console.log('达到最大重连次数，停止重连');
      return;
    }

    clearTimeout(this.reconnectTimer);
    this.reconnectAttempts++;
    
    const delay = Math.min(1000 * Math.pow(2, this.reconnectAttempts), 30000); // 指数退避，最大30秒
    console.log(`${delay}ms后进行第${this.reconnectAttempts}次重连`);
    
    this.reconnectTimer = setTimeout(() => {
      this.connect();
    }, delay);
  }

  // 注册消息处理器
  registerMessageHandler(pageId, handler) {
    this.messageHandlers.set(pageId, handler);
    console.log(`页面 ${pageId} 注册消息处理器`);
  }

  // 注销消息处理器
  unregisterMessageHandler(pageId) {
    this.messageHandlers.delete(pageId);
    console.log(`页面 ${pageId} 注销消息处理器`);
  }

  // 断开连接
  disconnect() {
    if (this.socketOpen) {
      wx.closeSocket();
      this.socketOpen = false;
    }
    clearTimeout(this.reconnectTimer);
    this.messageHandlers.clear();
  }

  // 发送消息
  sendMessage(message) {
    if (this.socketOpen) {
      wx.sendSocketMessage({
        data: JSON.stringify(message),
        success: () => {
          console.log('消息发送成功');
        },
        fail: (err) => {
          console.error('消息发送失败:', err);
        }
      });
    } else {
      console.warn('Socket未连接，无法发送消息');
    }
  }

  // 获取连接状态
  isConnected() {
    return this.socketOpen;
  }
}

// 创建全局单例
const socketManager = new SocketManager();

export default socketManager;
