import dictionaryApi from '../../api/modules/dictionary';
import orderApi from '../../api/modules/order';
import { formatDate } from '../utils/util';
import config from '../../api/config';

Page({
  data: {
    userInfo: null,
    // 订单类型标签
    orderTabs: [],
    currentTab: '', // 当前选中的标签
    orderList: [], // 订单列表
    page: 1, // 当前页码
    pageSize: 50, // 每页数量
    // 接单确认弹窗
    showConfirmModal: false,
    confirmModalTitle: '接单确认',
    confirmModalContent: '确定要接此订单吗？',
    confirmModalBtnText: '确认接单',
    pendingOrderId: '', // 待接单的订单ID
  },

  onLoad() {
    const _this = this;
    const userInfo = this.data.userInfo;
    if (!userInfo) {
      return wx.redirectTo({
        url: '/pages/login/index',
      });
    } else {
      // 加载字典数据
      dictionaryApi.list('服务类型').then(res => {
        this.setData({
          orderTabs: (res || []).map(item => {
            return {
              name: item.name + '单',
              status: item.code,
            };
          }),
          currentTab: (res || [])?.[0]?.code,
        });
        // 加载初始订单数据
        _this.loadOrders();
      });
    }

    // 使用全局socket管理器
    const app = getApp();
    const socketManager = app.globalData.socketManager;

    // 注册消息处理器
    socketManager.registerMessageHandler('index', (notification) => {
      this.handleSocketMessage(notification);
    });

    // 确保socket连接
    if (!socketManager.isConnected()) {
      socketManager.connect();
    }
  },

  // 切换订单状态标签
  switchTab(e) {
    const status = e.currentTarget.dataset.status;
    this.setData({
      currentTab: status,
      page: 1, // 重置页码
      orderList: [], // 清空当前列表
    });
    this.loadOrders();
  },

  // 加载订单数据
  async loadOrders() {
    wx.showLoading({
      title: '加载中',
    });

    const { id } = this.data.userInfo;
    const res = await orderApi.list(id, this.data.currentTab);
    const newData = res?.list.map(item => {
      return {
        ...item,
        serviceName: item.orderDetails.map(item => item.service.serviceName),
        serviceLogo: item.orderDetails.map(item => item.service.logo)[0],
        orderTime: formatDate(item.orderTime),
      };
    });

    // 根据当前状态过滤订单
    // const filteredOrders = this.filterOrdersByType(res?.list);

    this.setData({
      orderList: newData,
    });

    wx.hideLoading();
  },

  // 根据状态过滤订单
  filterOrdersByType(orders) {
    return orders.filter(order => order.type === this.data.currentTab);
  },

  // 获取订单状态文字
  getOrderStatusText(status) {
    const statusMap = {
      unpaid: '待付款',
      paid: '待接单',
      completed: '进行中',
      review: '待评价',
    };
    return statusMap[status] || '未知状态';
  },

  // 加载更多订单
  loadMoreOrders() {
    this.setData({
      page: this.data.page + 1,
    });
    this.loadOrders();
  },

  // 切换更多操作弹窗
  toggleOrderActions(e) {
    const orderId = e.currentTarget.dataset.orderId;
    const orderList = this.data.orderList.map(order => {
      if (order.orderId === orderId) {
        order.showMoreActions = !order.showMoreActions;
      } else {
        // 关闭其他订单的更多操作
        order.showMoreActions = false;
      }
      return order;
    });

    this.setData({
      orderList,
    });
  },

  // 阻止事件冒泡
  preventTap(e) {
    e.stopPropagation();
  },

  // 查看订单详情
  viewOrderDetail(e) {
    const orderInfo = e.currentTarget.dataset.item;
    wx.setStorageSync('orderInfo', orderInfo);
    wx.navigateTo({
      url: `/pages/orders/orderDetail/index`,
    });
  },

  // 删除订单
  deleteOrder(e) {
    const orderId = e.currentTarget.dataset.orderId;
    wx.showModal({
      title: '删除订单',
      content: '确定要删除此订单吗？',
      success: res => {
        if (res.confirm) {
          // 实际项目中调用删除订单API
          const orderList = this.data.orderList.filter(order => order.orderId !== orderId);
          this.setData({
            orderList,
          });
          wx.showToast({
            title: '删除成功',
            icon: 'success',
          });
        }
      },
    });
  },

  // 催接单
  confirmReceipt(e) {},

  // 显示接单确认弹窗
  reschedule(e) {
    const orderId = e.currentTarget.dataset.orderId;
    this.setData({
      showConfirmModal: true,
      pendingOrderId: orderId,
    });
  },

  // 确认接单
  async onConfirmAccept() {
    // 隐藏确认弹窗
    this.setData({
      showConfirmModal: false,
    });

    wx.showLoading({
      title: '接单中',
    });

    const orderId = this.data.pendingOrderId;
    const userId = this.data.userInfo.id;

    try {
      const res = await orderApi.accept(orderId, userId);
      if (res) {
        wx.showToast({
          title: '接单成功',
          icon: 'success',
          // duration: 1000, // 显示1秒
          mask: true, // 防止用户点击
        });

        // 0.5秒后跳转到我的订单页面（给toast足够的显示时间）
        setTimeout(() => {
          wx.switchTab({
            url: '/pages/orders/index',
          });
        }, 500);
      } else {
        wx.showToast({
          title: '接单失败',
          icon: 'error',
        });
      }
    } catch (error) {
      wx.showToast({
        title: '接单失败',
        icon: 'error',
      });
    } finally {
      // wx.hideLoading();
      this.loadOrders();
    }
  },

  // 取消接单
  onCancelAccept() {
    this.setData({
      showConfirmModal: false,
      pendingOrderId: '',
    });
  },

  // 处理Socket消息
  handleSocketMessage(notification) {
    console.log('首页收到Socket消息:', notification);

    switch (notification.type) {
      case 'new_order':
        // 更新订单列表
        this.loadOrders();
        break;
      case 'cancel_order':
        // 更新订单列表
        this.loadOrders();
        break;
      case 'message':
        // 消息已在socketManager中处理
        break;
    }
  },

  // 页面卸载时注销消息处理器
  onUnload() {
    const app = getApp();
    const socketManager = app.globalData.socketManager;
    socketManager.unregisterMessageHandler('index');
  },

  // ----------------------------旧的socket代码（已废弃）-----------------------
  // 建立WebSocket连接
  connectWebSocket: function () {
    // 替换为实际的后端WebSocket地址
    const socketUrl = config.socketUrl;

    // 检查是否已有连接
    if (this.data.socketOpen) {
      return;
    }

    // 打开WebSocket连接
    wx.connectSocket({
      url: socketUrl,
      success: res => {
        this.setData({
          socketOpen: true,
        });
        console.log('WebSocket连接成功');
      },
      fail: err => {
        console.error('WebSocket连接失败', err);
        // 可以添加重连逻辑
        this.reconnectWebSocket();
      },
    });

    // 监听WebSocket消息
    wx.onSocketMessage(res => {
      this.handleNotification(res.data);
    });

    // 监听WebSocket关闭
    wx.onSocketClose(res => {
      this.setData({
        socketOpen: false,
      });
      console.log('WebSocket连接关闭', res);
      // 自动重连
      this.reconnectWebSocket();
    });

    // 监听WebSocket错误
    wx.onSocketError(res => {
      console.error('WebSocket连接错误', res);
      // 重连逻辑
      this.reconnectWebSocket();
    });
  },

  // 处理接收到的通知
  handleNotification: function (message) {
    try {
      const notification = JSON.parse(message);
      console.log('收到新通知', notification);
      switch (notification.type) {
        case 'new_order':
          // 播放通知音效（可选）
          this.playNotificationSound('new');
          // 更新订单列表
          this.loadOrders();
          break;
        case 'cancel_order':
          // 播放通知音效（可选）
          this.playNotificationSound('cancel');
          // 更新订单列表
          this.loadOrders();
          break;
        case 'message':
          // 更新消息列表
          wx.showToast({
            title: notification.message,
            icon: 'none',
            duration: 2000,
          });
          break;
      }
    } catch (error) {
      console.error('解析通知消息失败', error);
    }
  },

  // 播放通知音效
  playNotificationSound: function (type) {
    // 使用小程序的音频API播放通知音效
    const audioContext = wx.createInnerAudioContext();
    if (type === 'new') {
      audioContext.src = 'https://xian7.zos.ctyun.cn/pet/static/message.mp3';
    }
    if (type === 'cancel') {
      audioContext.src = 'https://xian7.zos.ctyun.cn/pet/static/message1.mp3';
    }
    audioContext.autoplay = true;
    audioContext.play();
  },

  // 重连WebSocket
  reconnectWebSocket: function () {
    // 延迟重连，避免频繁连接
    clearTimeout(this.reconnectTimer);
    this.reconnectTimer = setTimeout(() => {
      this.connectWebSocket();
    }, 3000);
  },

  // 页面卸载时关闭连接
  onUnload: function () {
    if (this.data.socketOpen) {
      wx.closeSocket();
      this.setData({
        socketOpen: false,
      });
    }
  },
});
