.container {
  background-color: #f1f1f1;
  min-height: 100vh;
}

.order-tabs {
  display: flex;
  background-color: #fff;
  height: 120rpx;
  align-items: center;
  border-radius: 0 0 32rpx 32rpx;
  overflow: hidden;
}

.tab-item {
  flex: 1;
  text-align: center;
  color: #666;
  font-size: 28rpx;
  position: relative;
  line-height: 70rpx;
}

.tab-item.active {
  color: #333;
  font-weight: bold;
  font-size: 30rpx;
}

.tab-item.active::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 50%;
  transform: translateX(-50%);
  width: 30rpx;
  height: 8rpx;
  border-radius: 8rpx;
  background-color: #FF4391;
}

.order-list {
  height: calc(100vh - 80rpx);
}

.order-item {
  background-color: #fff;
  margin: 20rpx;
  border-radius: 10rpx;
  padding: 20rpx;
  position: relative;
}

.order-item .flex {
  width: 100%;
}

.order-content {
  display: flex;
  align-items: center;
  margin-bottom: 20rpx;
}

.product-image {
  width: 150rpx;
  height: 150rpx;
  margin-right: 20rpx;
  border-radius: 150rpx;
}

.product-info {
  display: flex;
  flex-direction: column;
  flex: 1;
}

.product-service {
  background: rgba(47, 131, 255, 0.1);
  border-radius: 8px;
  padding: 8rpx 12rpx;
  width: 100%;
  line-height: 50rpx;
  margin-top: 16rpx;
}

.product-name {
  font-size: 28rpx;
  color: #333;
  margin-bottom: 10rpx;
}

.magin-bottom {
  margin-bottom: 20rpx;
  align-items: baseline;
}

.magin-bottom text:nth-child(1) {
  width: 260rpx;
}

.more-btn {
  margin-right: 20rpx;
  color: rgba(47, 131, 255, 1);
  font-size: 24rpx;
  position: relative;
}

.more-actions-dropdown {
  position: absolute;
  top: 100%;
  left: 0;
  width: 260rpx;
  background-color: #fff;
  border-radius: 10rpx;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  z-index: 100;
  overflow: hidden;
}

.dropdown-item {
  padding: 20rpx;
  text-align: center;
  font-size: 24rpx;
  color: #333;
  border-bottom: 1rpx solid #eee;
  background-color: #fff;
  transition: background-color 0.3s;
}

.dropdown-item:last-child {
  border-bottom: none;
}

.dropdown-item:hover {
  background-color: #f4f4f4;
}

.order-actions {
  display: flex;
  justify-content: flex-end;
  align-items: center;
}

.action-btn {
  padding: 10rpx 20rpx;
  border-radius: 5rpx;
  font-size: 24rpx;
  background: rgba(238, 238, 238, 1);
  color: rgba(51, 51, 51, 1);
  border-radius: 40rpx;
  margin-left: 20rpx;
}

.action-btn:nth-last-child(1) {
  background: rgba(47, 131, 255, 1);
  color: white;
  border-radius: 40rpx;
}

.empty-list {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  padding-top: 200rpx;
}

.empty-image {
  width: 300rpx;
  height: 300rpx;
}

.empty-text {
  margin-top: 20rpx;
  color: #999;
}

.titleTab {
  margin-right: 25rpx;
  font-size: 30rpx;
}

.titleTab:first-child {
  margin-left: 35rpx;
}

.titleTabActive {
  font-weight: bold;
  font-size: 36rpx;
  color: #3083FF;
}